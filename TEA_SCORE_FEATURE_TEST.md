# 茶水记分功能测试指南

## 功能概述
实现了茶水单独记分功能，用户可以点击茶水按钮弹出记分弹窗，在标题左侧有设置图标可以快速访问茶水设置。

## 实现的功能

### 1. 茶水记分界面
- 点击茶水按钮弹出 score-modal 组件
- 自定义标题显示"给茶水记分"
- 标题左侧有设置图标，点击可打开茶水设置弹窗
- 显示茶水图标而不是头像
- 输入框提示文字为"填写茶水金额"

### 2. 数据处理逻辑
- 复用现有的茶水余额更新逻辑
- 支持 WebSocket 实时同步
- 降级到云对象处理
- 添加系统消息记录
- 支持流水记录（给分玩法）

### 3. WebSocket 同步
- 新增 `tea_score` 消息类型
- 实时广播给房间内其他玩家
- 更新茶水余额
- 添加系统消息

## 测试步骤

### 前置条件
1. 启动开发服务器：`npm run dev:mp-weixin`
2. 在微信开发者工具中打开项目
3. 创建或加入一个房间

### 测试用例

#### 测试用例 1：基本茶水记分功能
1. 点击左侧茶水按钮
2. 验证弹出的弹窗标题为"给茶水记分"
3. 验证标题左侧有设置图标
4. 验证显示茶水图标而不是头像
5. 输入金额（如：100）
6. 点击"确定计分"
7. 验证茶水余额更新
8. 验证显示成功提示

#### 测试用例 2：设置图标功能
1. 点击茶水按钮打开记分弹窗
2. 点击标题左侧的设置图标
3. 验证茶水设置弹窗打开
4. 修改茶水设置并保存
5. 验证设置生效

#### 测试用例 3：WebSocket 同步测试
1. 在两个设备/浏览器中打开同一房间
2. 在设备A中进行茶水记分
3. 验证设备B实时收到茶水余额更新
4. 验证设备B显示系统消息

#### 测试用例 4：数据持久化测试
1. 进行茶水记分
2. 刷新页面或重新进入房间
3. 验证茶水余额正确显示
4. 验证消息记录存在

## 预期结果

### 成功标准
- [ ] 茶水按钮点击正常弹出记分弹窗
- [ ] 自定义标题显示正确
- [ ] 设置图标功能正常
- [ ] 茶水记分数据正确更新
- [ ] WebSocket 实时同步正常
- [ ] 系统消息正确记录
- [ ] 数据持久化正常

### 错误处理
- [ ] 输入无效金额时显示错误提示
- [ ] 网络异常时降级处理正常
- [ ] WebSocket 连接失败时云对象处理正常

## 代码修改总结

### 修改的文件
1. `components/score-modal.vue` - 支持自定义标题和茶水模式
2. `pages/room/index.vue` - 添加茶水点击事件和自定义标题
3. `pages/room/index.scss` - 添加茶水弹窗标题样式
4. `pages/room/mixins/scoringMixin.js` - 添加茶水记分处理逻辑
5. `pages/room/mixins/websocketMixin.js` - 添加茶水记分 WebSocket 方法
6. `uniCloud-alipay/cloudfunctions/room-websocket/index.js` - 添加茶水记分云函数处理

### 新增功能
- 茶水记分模式 (`modalType: 'tea'`)
- 茶水点击处理 (`handleTeaClick`)
- 茶水记分处理 (`handleTeaScore`)
- WebSocket 茶水记分 (`wsTeaScore`)
- 云函数茶水记分处理 (`handleTeaScore`)

## 注意事项
1. 茶水记分直接更新余额，不进行抽佣计算
2. 复用现有的茶水设置和计算逻辑
3. 保持与现有功能的兼容性
4. 支持传统玩法和给分玩法两种模式
