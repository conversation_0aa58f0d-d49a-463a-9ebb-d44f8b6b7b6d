# 茶水记分问题调试

## 问题描述
1. 茶水增加了200，但是给分的人变成了-400（分数被减了两次）
2. 报错：WebSocket茶水记分失败: Error: 不支持的操作类型: tea_score

## 问题分析

### 问题1：分数被减两次
**原因**：在云对象降级处理中，既调用了`addRoomMessage`（云对象中会更新玩家分数），又在前端调用了`updatePlayerScore`

**修复**：已移除前端的重复分数更新

### 问题2：WebSocket错误
**可能原因**：
1. 云函数没有正确部署
2. 消息格式问题
3. 其他拦截逻辑

**检查点**：
- [x] switch语句中有tea_score案例
- [x] handleTeaScore函数存在
- [x] 前端发送消息格式正确
- [ ] 云函数是否正确部署
- [ ] 是否有其他拦截逻辑

## 调试步骤

### 1. 检查云函数部署
```bash
cd uniCloud-alipay
npx unicloud-cli deploy --cloud-function room-websocket
```

### 2. 检查WebSocket连接状态
在前端添加调试日志：
```javascript
console.log('WebSocket状态:', this.isWebSocketEnabled);
console.log('发送茶水记分消息:', { action: 'tea_score', amount: score });
```

### 3. 检查云函数日志
在云函数中添加更多日志：
```javascript
console.log('收到WebSocket消息:', { action, data });
```

### 4. 验证消息路由
确认消息是否到达handleTeaScore函数

## 修复进展

### ✅ 已修复的问题
1. **分数被减两次** - 移除了前端重复的分数更新
2. **实时同步问题** - 在云对象addRoomMessage中添加了广播逻辑

### 🔧 修复内容
1. **云对象广播逻辑**：
   - 在addRoomMessage方法中添加了WebSocket广播
   - 构建正确的广播消息格式
   - 包含茶水记分的增量数据

2. **前端广播处理**：
   - 添加了tea_score类型的通知处理
   - 复用现有的增量更新逻辑

### 📊 数据流转
1. 用户点击茶水记分 → 云对象处理
2. 云对象更新数据库 → 构建广播消息
3. 广播给房间内其他玩家 → 前端增量更新
4. 其他玩家实时看到分数和茶水余额变化

## 测试验证
现在请测试：
1. 在多个设备中打开同一房间
2. 在设备A进行茶水记分
3. 验证设备B是否实时收到更新
