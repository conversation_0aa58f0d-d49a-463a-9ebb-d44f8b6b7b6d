@import "@/styles/common.scss";

// ==================== 茶水记分弹窗样式 ====================
.tea-modal-title {
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  .settings-icon {
    font-size: 18px;
    color: $primary-color;
    cursor: pointer;
    padding: $spacing-xs;
    border-radius: $border-radius-round;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba($primary-color, 0.1);
    }

    &:active {
      transform: scale(0.95);
      background-color: rgba($primary-color, 0.2);
    }
  }

  .title-text {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }
}

// ==================== 基础布局 ====================
.room-container {
  @include page-container;
  display: flex;
  width: 100%;
  height: 100vh;
}

// ==================== 左侧工具栏 ====================
.sidebar {
  width: 80px;
  min-width: 80px;
  height: 100%;
  background-color: $card-background;
  border-right: 1px solid $border-color;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: $box-shadow-sm;
}

.top-players-area {
  display: flex;
  flex-direction: column;
  width: 100%;
  flex: 1;
  overflow: hidden;
}

.sidebar-item {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-sm;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: $border-radius-sm;
  margin: 2px;

  &:hover {
    background-color: $background-color;
  }

  &:active {
    transform: scale(0.98);
    background-color: $border-light;
  }
}

.players-list {
  width: 100%;
  flex: 1;
  height: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 $spacing-xs;
}

.player-item {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-xs;
  position: relative;
  border-radius: $border-radius-sm;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: $background-color;
  }
}

.invite-friends,
.tea-button {
  flex-shrink: 0;
}

// ==================== 茶水头像样式 ====================
.tea-avatar-wrapper {
  position: relative;
  margin-bottom: $spacing-xs;
  cursor: pointer;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tea-score-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  min-width: 18px;
  height: 18px;
  padding: $spacing-xs $spacing-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-bold;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: $border-radius-base;
  white-space: nowrap;
  max-width: 36px;
  overflow: hidden;
  border: 1px solid $card-background;

  &.positive {
    color: $text-white;
    background: linear-gradient(135deg, $positive-amount-color 0%, #dc2626 100%);
  }

  &.negative {
    color: $text-white;
    background: linear-gradient(135deg, $negative-amount-color 0%, #15803d 100%);
  }
}

// ==================== 头像样式 ====================
.player-avatar-wrapper {
  position: relative;
  margin-bottom: $spacing-xs;
  cursor: pointer;
  width: 44px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar {
  @include avatar(44px);
  border: 2px solid transparent;
  transition: all 0.3s ease;

  &.positive-bg {
    border-color: $positive-amount-color;
    box-shadow: 0 0 8px rgba(250, 81, 81, 0.2);
  }

  &.negative-bg {
    border-color: $negative-amount-color;
    box-shadow: 0 0 8px rgba(7, 193, 96, 0.2);
  }

  &.function-avatar {
    @include icon-container(44px, $primary-light);
    border-color: $border-color;

    &:hover {
      border-color: $primary-color;
      transform: scale(1.05);
    }

    &.tea-avatar {
      background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
      border-color: #f59e0b;

      &:hover {
        border-color: #d97706;
      }
    }
  }
}

.score-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  min-width: 18px;
  height: 18px;
  padding: $spacing-xs $spacing-sm;
  font-size: $font-size-xs;
  font-weight: $font-weight-bold;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border-radius: $border-radius-base;
  white-space: nowrap;
  max-width: 36px;
  overflow: hidden;
  border: 1px solid $card-background;

  &.positive {
    color: $text-white;
    background: linear-gradient(135deg, $positive-amount-color 0%, #dc2626 100%);
  }

  &.negative {
    color: $text-white;
    background: linear-gradient(135deg, $negative-amount-color 0%, #15803d 100%);
  }
}

.player-name,
.function-name {
  font-size: $font-size-xs;
  color: $text-secondary;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  max-width: 60px;
  line-height: 1.2;
  margin-top: $spacing-xs;
}

.function-icon {
  font-size: 20px;
  color: $text-secondary;
  transition: color 0.2s ease;

  .sidebar-item:hover & {
    color: $primary-color;
  }
}

// ==================== 底部功能按钮 ====================
.bottom-buttons {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: $spacing-sm;
  border-top: 1px solid $border-color;
  flex-shrink: 0;
  background: linear-gradient(
    to bottom,
    $card-background 0%,
    $background-color 100%
  );
}

.function-button {
  width: 100%;
  padding: $spacing-sm;
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  border-radius: $border-radius-sm;
  transition: all 0.2s ease;
  margin-bottom: 2px;

  &:hover {
    background-color: $background-color;
    transform: translateY(-1px);
  }

  &:active {
    transform: scale(0.98);
    background-color: $border-light;
  }
}

.bottom-icon {
  font-size: 18px;
  color: $text-muted;
  margin-bottom: $spacing-xs;
  transition: color 0.2s ease;

  .function-button:hover & {
    color: $text-secondary;
  }
}

// ==================== 右侧内容区域 ====================
.content {
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
  background-color: $background-color;
}

.top-hint {
  @include card;
  padding: $spacing-sm $spacing-md;
  font-size: $font-size-sm;
  color: $text-secondary;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid $border-color;
  height: 36px;
  margin: 0;
  border-radius: 0;
}

.hint-scroll-container {
  flex: 1;
  margin-right: $spacing-sm;
  overflow: hidden;
  position: relative;
  height: 20px;
}

.hint-text-wrapper {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}

.hint-text {
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.5s ease-in-out;
  font-size: $font-size-sm;
  color: $text-secondary;
  width: 100%;

  &.active {
    opacity: 1;
  }
}

.network-status {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  font-size: $font-size-xs;
  cursor: pointer;
  padding: $spacing-xs $spacing-sm;
  border-radius: $border-radius-sm;
  transition: all 0.2s ease;

  &:hover {
    background-color: $background-color;
  }

  &.good {
    color: $success-color;
  }

  &.poor {
    color: $warning-color;
  }

  &.disconnected {
    color: $accent-color;
  }
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: $border-radius-round;
  margin-left: $spacing-xs;
  transition: background-color 0.3s ease;

  &.good {
    background-color: $success-color;
  }

  &.poor {
    background-color: $warning-color;
  }

  &.disconnected {
    background-color: $accent-color;
  }
}

// ==================== 聊天记分区域 ====================
.messages-area {
  flex: 1;
  overflow: hidden;
  padding: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
}

.messages-scroll-view {
  flex: 1;
  height: 100%;
  width: 100%;
  padding: $spacing-md;
}

// ==================== 桌面区域 ====================
.table-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
  padding: $spacing-md;
  overflow-y: auto;
  overflow-x: hidden;
  width: 100%;
}

.table-container {
  @include card-shadow;
  width: 90%;
  max-width: 400px;
  background: linear-gradient(135deg, $primary-color 0%, $primary-dark 100%);
  border-radius: $border-radius-lg;
  padding: $spacing-lg;
  display: flex;
  flex-direction: column;
  margin-bottom: $spacing-md;
  cursor: pointer;
  transition: all 0.2s ease;
  color: $text-white;

  &:hover {
    transform: translateY(-2px);
    box-shadow: $box-shadow-lg;
  }
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-lg;
}

.round-number,
.record-link {
  font-size: $font-size-base;
  color: $text-white;
  font-weight: $font-weight-medium;
}

.record-link {
  opacity: 0.8;
  transition: opacity 0.2s ease;

  &:hover {
    opacity: 1;
  }
}

.table-center {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: $spacing-md;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: $border-radius-round;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.score-value {
  font-size: $font-size-huge;
  font-weight: $font-weight-bold;
  color: $text-white;
}

.score-label {
  font-size: $font-size-base;
  color: $text-white;
  margin-top: $spacing-xs;
  opacity: 0.9;
}

.table-players-container {
  width: 100%;
  padding: $spacing-sm;
  margin-top: $spacing-sm;
}

.table-players {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: $spacing-md;
  justify-content: center;
  width: 100%;
}

.table-player {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
}

.player-avatar {
  @include avatar(50px);
  margin-bottom: $spacing-sm;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: scale(1.05);
  }

  &.positive-bg {
    border-color: $positive-amount-color;
  }

  &.negative-bg {
    border-color: $negative-amount-color;
  }
}

.player-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  .player-name {
    font-size: $font-size-sm;
    color: $secondary-color;
    text-align: center;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    margin-bottom: $spacing-xs;
    font-weight: $font-weight-medium;
  }
}

.player-status {
  font-size: $font-size-xs;
  color: $text-secondary;
  margin-top: 1px;
}

// ==================== 底部操作按钮 ====================
.bottom-actions {
  @include card;
  height: 70px;
  display: flex;
  padding: $spacing-md;
  gap: $spacing-md;
  border-top: 1px solid $border-color;
  margin: 0;
  border-radius: 0;
  background: $card-background;

  .action-button {
    @include button-base;
    flex: 1;
    height: 44px;
    border-radius: $border-radius-xl;
    font-size: $font-size-base;
    font-weight: $font-weight-medium;
    transition: all 0.2s ease;

    &:active {
      transform: scale(0.98);
    }

    &.settlement {
      @include button-secondary;

      &:active {
        background-color: $background-color;
      }
    }

    &.record,
    &.distribution {
      @include button-primary;
      background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);

      &:active {
        background: linear-gradient(135deg, #d97706 0%, #b45309 100%);
      }
    }

    &.scoring {
      background: linear-gradient(135deg, $accent-color 0%, #dc2626 100%);
      color: $text-white;

      &:active {
        background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
      }
    }
  }
} 