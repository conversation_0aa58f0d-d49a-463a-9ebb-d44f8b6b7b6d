// WebSocket管理器 Mixin
// 负责处理WebSocket连接、消息发送、断线重连等功能

import { RoomService } from "@/utils/room.js";
import { showToast } from "@/utils/commonMethods.js";
import { safeAdd } from "@/utils/mathUtils.js";

export default {
  data() {
    return {
      // WebSocket连接状态
      wsConnected: false,
      wsConnecting: false,
      wsSocket: null,
      
      // 重连管理
      wsReconnectAttempts: 0,
      wsMaxReconnectAttempts: 5,
      wsReconnectTimer: null,
      
      // 请求管理
      pendingRequests: new Map(), // requestId -> { resolve, reject, timestamp }
      requestTimeout: 10000, // 10秒超时
      requestDeduplication: new Map(), // 请求去重 action+data -> { timestamp, requestId }
      
      // WebSocket配置
      wsConfig: {
        url: '', // 将在initWebSocket中设置
        token: '',
        roomId: ''
      },
      
      // 房间在线状态
      roomOnlineCount: 0,
      showOnlineStatus: true
    };
  },

  computed: {
    // WebSocket连接状态文本
    wsStatusText() {
      if (this.wsConnecting) return '连接中...';
      if (this.wsConnected) return '已连接';
      return '未连接';
    },
    
    // 是否启用WebSocket功能
    isWebSocketEnabled() {
      return this.wsConnected && !this.wsConnecting;
    },
    
    // 在线状态显示文本
    onlineStatusText() {
      if (!this.showOnlineStatus) return '';
      if (this.roomOnlineCount <= 0) return '';
      return `在线 ${this.roomOnlineCount} 人`;
    },
    
    // 在线状态样式类
    onlineStatusClass() {
      return {
        'online-status': true,
        'online-status-connected': this.wsConnected,
        'online-status-disconnected': !this.wsConnected
      };
    }
  },

  methods: {
    // ==================== WebSocket连接管理 ====================
    
    /**
     * 初始化WebSocket连接
     * @param {string} roomId 房间ID
     */
    async initWebSocket(roomId) {
      try {
        if (this.wsConnecting || this.wsConnected) {
          console.log('WebSocket已连接或正在连接中');
          return;
        }

        // 获取用户token
        const token = RoomService.getUserToken();
        if (!token) {
          console.warn('用户token不存在，跳过WebSocket连接');
          return;
        }

        this.wsConfig.token = token;
        this.wsConfig.roomId = roomId;
        
        console.log('正在初始化WebSocket连接...');
        this.wsConnecting = true;

        // 使用uniCloud.connectWebSocket连接
        const socketTask = await uniCloud.connectWebSocket({
          name: "room-websocket",
          query: {
            token: token,
            roomId: roomId
          }
        });

        this.wsSocket = socketTask;
        this.setupWebSocketEvents();
        
      } catch (error) {
        console.error('初始化WebSocket连接失败:', error);
        this.wsConnecting = false;
        this.handleWebSocketError(error);
      }
    },

    /**
     * 设置WebSocket事件监听
     */
    setupWebSocketEvents() {
      if (!this.wsSocket) return;

      // 连接成功事件
      this.wsSocket.onOpen((event) => {
        console.log('WebSocket连接已建立:', event);
        this.wsConnected = true;
        this.wsConnecting = false;
        this.wsReconnectAttempts = 0;
        
        // 清除重连定时器
        if (this.wsReconnectTimer) {
          clearTimeout(this.wsReconnectTimer);
          this.wsReconnectTimer = null;
        }
        
        showToast('实时同步已开启', 'success');
      });

      // 收到消息事件
      this.wsSocket.onMessage((event) => {
        try {
          const message = JSON.parse(event.data);
          console.log('收到WebSocket消息:', message);
          this.handleWebSocketMessage(message);
        } catch (parseError) {
          console.error('解析WebSocket消息失败:', parseError, event.data);
        }
      });

      // 连接关闭事件
      this.wsSocket.onClose((event) => {
        console.log('WebSocket连接已关闭:', event);
        this.wsConnected = false;
        this.wsConnecting = false;
        
        // 清理待处理的请求
        this.clearPendingRequests('连接已关闭');
        
        // 尝试重连
        this.scheduleReconnect();
      });

      // 连接错误事件
      this.wsSocket.onError((event) => {
        console.error('WebSocket连接错误:', event);
        this.wsConnected = false;
        this.wsConnecting = false;
        this.handleWebSocketError(event);
      });
    },

    /**
     * 处理WebSocket消息
     * @param {Object} message WebSocket消息
     */
    handleWebSocketMessage(message) {
      const { action, success, data, requestId, message: errorMessage } = message;

      // 处理连接相关消息
      if (action === 'connection_success') {
        console.log('WebSocket连接验证成功');
        
        // 更新在线人数
        if (data && typeof data.onlineCount === 'number') {
          this.updateOnlineCount(data.onlineCount);
        }
        return;
      }
      
      if (action === 'connection_error') {
        console.error('WebSocket连接验证失败:', errorMessage);
        // 不在页面切换等场景显示错误提示，避免用户体验问题
        // 只在特定错误情况下显示提示
        if (errorMessage && !errorMessage.includes('无权限访问该房间')) {
          showToast(errorMessage || 'WebSocket连接失败', 'none');
        }
        this.closeWebSocket();
        return;
      }

      // 处理房间广播消息
      if (action === 'room_broadcast') {
        this.handleRoomBroadcast(message);
        return;
      }

      // 处理玩家上线/离线通知
      if (action === 'player_online') {
        this.handlePlayerOnline(data);
        return;
      }
      
      if (action === 'player_offline') {
        this.handlePlayerOffline(data);
        return;
      }

      // 处理新用户加入通知
      if (action === 'player_join_notification') {
        this.handlePlayerJoinNotification(data);
        return;
      }

      // 处理欢迎消息
      if (action === 'welcome_message') {
        this.handleWelcomeMessage(data);
        return;
      }

      // 处理业务请求的响应
      if (requestId && this.pendingRequests.has(requestId)) {
        const request = this.pendingRequests.get(requestId);
        this.pendingRequests.delete(requestId);
        
        // 清理对应的去重记录
        for (const [key, value] of this.requestDeduplication) {
          if (value.requestId === requestId) {
            this.requestDeduplication.delete(key);
            break;
          }
        }
        
        if (success) {
          // 处理增量更新
          if (data && data.incremental) {
            this.handleIncrementalUpdate(data.incremental, action);
          }
          request.resolve(data);
        } else {
          request.reject(new Error(errorMessage || '操作失败'));
        }
      }
    },

    /**
     * 处理增量更新
     * @param {Object} incrementalData 增量数据
     * @param {string} action 操作类型
     */
    handleIncrementalUpdate(incrementalData, action) {
      try {
        console.log('处理增量更新:', incrementalData, action);

        // 更新玩家分数
        if (incrementalData.players && Array.isArray(incrementalData.players)) {
          incrementalData.players.forEach(playerUpdate => {
            const player = this.players.find(p => p.id === playerUpdate.id);
            if (player && typeof playerUpdate.scoreChange === 'number') {
              player.score = safeAdd(player.score || 0, playerUpdate.scoreChange);
              console.log(`玩家 ${player.name} 分数更新: ${playerUpdate.scoreChange}, 当前分数: ${player.score}`);
            }
          });
        }

        // 更新茶水余额
        if (typeof incrementalData.teaWaterBalance === 'number') {
          this.teaWaterBalance = incrementalData.teaWaterBalance;
          console.log('茶水余额更新:', this.teaWaterBalance);
        }

        // 处理新加入的玩家
        if (incrementalData.newPlayer) {
          const newPlayer = incrementalData.newPlayer;
          console.log('处理新加入的玩家:', newPlayer);
          
          // 转换字段名以保持前端一致性：avatar -> avatarFileId
          const normalizedNewPlayer = {
            ...newPlayer,
            avatarFileId: newPlayer.avatar || newPlayer.avatarFileId || '',
          };
          
          // 检查玩家是否已经存在
          const existingPlayerIndex = this.players.findIndex(p => p.id === normalizedNewPlayer.id);
          if (existingPlayerIndex !== -1) {
            // 更新已存在的玩家信息，使用Vue.set确保响应式更新
            this.$set(this.players, existingPlayerIndex, { ...this.players[existingPlayerIndex], ...normalizedNewPlayer });
            console.log('更新已存在的玩家:', this.players[existingPlayerIndex]);
          } else {
            // 添加新玩家，使用push方法自动触发响应式更新
            this.players.push(normalizedNewPlayer);
            console.log('添加新玩家:', normalizedNewPlayer);
          }
        }

        // 处理玩家离开
        if (incrementalData.playerLeft) {
          const leftPlayer = incrementalData.playerLeft;
          console.log('处理玩家离开:', leftPlayer);
          
          const player = this.players.find(p => p.id === leftPlayer.id);
          if (player) {
            player.hasLeft = true;
            console.log(`玩家 ${player.name} 已标记为离开`);
          }
        }

        // 处理更新的玩家列表
        if (incrementalData.updatedPlayers && Array.isArray(incrementalData.updatedPlayers)) {
          console.log('处理更新的玩家列表:', incrementalData.updatedPlayers);
          
          // 更新每个玩家的状态
          incrementalData.updatedPlayers.forEach(updatedPlayer => {
            const existingPlayer = this.players.find(p => p.id === updatedPlayer.id);
            if (existingPlayer) {
              // 转换字段名以保持前端一致性：avatar -> avatarFileId
              const normalizedUpdatedPlayer = {
                ...updatedPlayer,
              };
              if (updatedPlayer.avatar && !updatedPlayer.avatarFileId) {
                normalizedUpdatedPlayer.avatarFileId = updatedPlayer.avatar;
                delete normalizedUpdatedPlayer.avatar;
              }
              
              // 更新现有玩家信息（Vue会自动检测对象属性的变化）
              Object.assign(existingPlayer, normalizedUpdatedPlayer);
              console.log('更新玩家信息:', existingPlayer);
            }
          });
        }

        // 更新茶水设置
        if (incrementalData.teaWaterSettings) {
          const { teaWaterLimitAmount, teaWaterRatio } = incrementalData.teaWaterSettings;
          
          // 确保 teaWaterRatio 的类型正确 - 数值类型
          if (teaWaterRatio !== undefined) {
            const ratio = Number(teaWaterRatio);
            if (!isNaN(ratio)) {
              this.teaWaterRatio = ratio;
              console.log('茶水比例更新:', this.teaWaterRatio);
            }
          }
          
          // 确保 teaWaterLimitAmount 的类型正确 - 数值类型或null
          if (teaWaterLimitAmount !== undefined) {
            if (teaWaterLimitAmount === null) {
              this.teaWaterLimitAmount = null;
            } else {
              const limitAmount = Number(teaWaterLimitAmount);
              this.teaWaterLimitAmount = isNaN(limitAmount) ? null : limitAmount;
            }
            console.log('茶水上限更新:', this.teaWaterLimitAmount);
          }
        }

        // 添加新消息记录（支持单条消息和多条消息）
        let hasNewTableOperationMessage = false;
        let hasNewScoreMessage = false;
        let newMessagesForVoice = []; // 用于语音播报的新消息
        
        if (incrementalData.newMessage) {
          console.log('处理新消息:', incrementalData.newMessage);
          if (this.messageData && Array.isArray(this.messageData)) {
            this.messageData.push(incrementalData.newMessage);
            console.log('新消息已添加:', incrementalData.newMessage.type, '消息内容:', incrementalData.newMessage.message);
            
            // 添加到语音播报队列
            newMessagesForVoice.push(incrementalData.newMessage);
            
            // 检查是否是桌面操作相关的消息（出分或收分）
            if (incrementalData.newMessage.type === 'distribute' || incrementalData.newMessage.type === 'collect') {
              hasNewTableOperationMessage = true;
              console.log('检测到桌面操作消息:', incrementalData.newMessage.type);
            }
            
            // 检查是否是计分相关的消息
            if (incrementalData.newMessage.type === 'score' || incrementalData.newMessage.type === 'settlement') {
              hasNewScoreMessage = true;
              console.log('检测到计分相关消息:', incrementalData.newMessage.type);
            }
            
            // 滚动到底部（如果是传统记分视图）
            if (this.isTraditionalView && this.scrollToBottom) {
              this.$nextTick(() => {
                this.scrollToBottom();
              });
            }
          } else {
            console.warn('messageData 不是数组或为空，无法添加新消息');
          }
        } else if (incrementalData.newMessages && Array.isArray(incrementalData.newMessages)) {
          console.log('处理多条新消息:', incrementalData.newMessages.length);
          if (this.messageData && Array.isArray(this.messageData)) {
            this.messageData.push(...incrementalData.newMessages);
            console.log('批量消息已添加:', incrementalData.newMessages.length, '条');
            
            // 添加到语音播报队列
            newMessagesForVoice.push(...incrementalData.newMessages);
            
            // 检查是否包含桌面操作相关的消息
            hasNewTableOperationMessage = incrementalData.newMessages.some(msg => 
              msg.type === 'distribute' || msg.type === 'collect'
            );
            if (hasNewTableOperationMessage) {
              console.log('批量消息中检测到桌面操作消息');
            }
            
            // 检查是否包含计分相关的消息
            hasNewScoreMessage = incrementalData.newMessages.some(msg => 
              msg.type === 'score' || msg.type === 'settlement'
            );
            if (hasNewScoreMessage) {
              console.log('批量消息中检测到计分相关消息');
            }
            
            // 滚动到底部（如果是传统记分视图）
            if (this.isTraditionalView && this.scrollToBottom) {
              this.$nextTick(() => {
                this.scrollToBottom();
              });
            }
          }
        } else {
          console.log('未发现新消息数据');
        }
        
        // 处理局结束消息
        if (incrementalData.roundMessages && Array.isArray(incrementalData.roundMessages)) {
          console.log('处理局结束消息:', incrementalData.roundMessages.length);
          if (this.messageData && Array.isArray(this.messageData)) {
            this.messageData.push(...incrementalData.roundMessages);
            console.log('局结束消息已添加:', incrementalData.roundMessages.length, '条');
            
            // 添加到语音播报队列（局结束消息也需要播报）
            newMessagesForVoice.push(...incrementalData.roundMessages);
            
            // 检查是否包含局状态变化的消息
            const hasRoundStateMessage = incrementalData.roundMessages.some(msg => 
              msg.type === 'round_end' || msg.type === 'round_start'
            );
            if (hasRoundStateMessage) {
              console.log('检测到局状态变化消息，需要更新局状态');
              hasNewTableOperationMessage = true; // 触发局状态更新
            }
            
            // 滚动到底部（如果是传统记分视图）
            if (this.isTraditionalView && this.scrollToBottom) {
              this.$nextTick(() => {
                this.scrollToBottom();
              });
            }
          }
        }

        // 语音播报新消息（对于所有玩法，包括给分玩法）
        // 需要在所有消息处理完成后统一播报
        if (newMessagesForVoice.length > 0) {
          console.log('准备播报新消息:', newMessagesForVoice.length, '条');
          // 调用 voiceMixin 的语音播报方法
          if (this.speakNewMessages && typeof this.speakNewMessages === 'function') {
            this.speakNewMessages(newMessagesForVoice);
          } else {
            console.warn('语音播报方法不可用');
          }
        }

        // 处理完所有增量更新后，检查是否需要重新计算分数
        try {
          let shouldRecalculateScores = false;
          let shouldUpdateRoundState = false;
          
          // 检查玩家列表是否发生变化
          if (incrementalData.newPlayer || 
              incrementalData.playerLeft || 
              incrementalData.updatedPlayers) {
            shouldRecalculateScores = true;
          }
          
          // 检查是否有桌面操作消息（出分或收分）或局状态变化消息，这种情况下需要更新局状态
          if (hasNewTableOperationMessage && !this.isTraditionalView) {
            shouldUpdateRoundState = true;
            console.log('检测到桌面操作或局状态变化消息，需要更新局状态');
          }
          
          // 如果玩家列表发生变化且存在消息数据，重新计算分数
          if (shouldRecalculateScores && 
              this.calculatePlayersScoreFromMessages && 
              this.messageData && 
              this.messageData.length > 0) {
            console.log('检测到玩家列表变化，重新计算所有玩家分数');
            this.calculatePlayersScoreFromMessages();
            console.log('分数重算完成，确保数据一致性');
          }
          
          // 如果有桌面操作消息或局状态变化且是给分玩法，更新局状态和桌面分数
          if (shouldUpdateRoundState && 
              this.updateCurrentRoundFromMessages && 
              this.messageData && 
              this.messageData.length > 0) {
            console.log('检测到桌面操作或局状态变化，重新计算局状态和桌面分数');
            this.updateCurrentRoundFromMessages();
            console.log('局状态和桌面分数重算完成，确保数据一致性');
          }

          // 传统玩法：重新计算当前用户流水
          if (this.isTraditionalView && 
              (shouldRecalculateScores || hasNewScoreMessage) && 
              this.calculateCurrentUserTransactions && 
              this.messageData && 
              this.messageData.length > 0) {
            console.log('传统玩法检测到分数变化，重新计算当前用户流水');
            this.calculateCurrentUserTransactions();
            console.log('传统玩法当前用户流水重算完成');
          }

          // 如果是给分玩法且有桌面操作消息，重新计算个人记录和流水
          if (!this.isTraditionalView && 
              hasNewTableOperationMessage && 
              this.messageData && 
              this.messageData.length > 0) {
            console.log('检测到桌面操作消息，重新计算个人记录和流水');
            
            // 重新计算所有玩家的个人流水
            if (this.calculateAllPlayersTransactions) {
              this.calculateAllPlayersTransactions();
              console.log('个人流水重算完成');
            }
            
            // 重新计算所有玩家的个人记录
            if (this.calculateAllPlayersRecords) {
              this.calculateAllPlayersRecords();
              console.log('个人记录重算完成');
            }
          }
        } catch (error) {
          console.error('分数或局状态重算失败，但不影响其他功能:', error);
        }

        console.log('增量更新处理完成');
      } catch (error) {
        console.error('处理增量更新异常:', error);
      }
    },

    /**
     * 处理房间广播消息
     * @param {Object} message 广播消息
     */
    handleRoomBroadcast(message) {
      console.log(message,'message---')
      try {
        const { action:operationType, data, operator, incremental } = message;

        console.log(`收到房间广播: ${operationType}, 操作者: ${data?.nickname || operator?.name}`);

        // 处理增量更新
        if (incremental) {
          this.handleIncrementalUpdate(incremental, operationType);
        }

        // 根据操作类型显示不同的提示
        this.showBroadcastNotification(operationType, {name: data?.nickname || operator?.name}, message);

      } catch (error) {
        console.error('处理房间广播消息失败:', error);
      }
    },

    /**
     * 显示广播通知
     * @param {string} operationType 操作类型
     * @param {Object} operator 操作者信息
     * @param {Object} broadcastData 广播数据
     */
    showBroadcastNotification(operationType, operator, broadcastData) {
      console.log(operationType, operator, broadcastData,'showBroadcastNotification---')
      try {
        let notificationText = '';

        switch (operationType) {
          case 'single_score':
            const { target, scoreChange } = broadcastData;
            notificationText = `${operator?.name} 给 ${target.name} 记分 ${scoreChange.actual}`;
            break;

          case 'multiple_score':
            const { targets, totalScoreChange } = broadcastData;
            notificationText = `${operator?.name} 给 ${targets.length} 位玩家记分，总计 ${totalScoreChange.actual}`;
            break;

          case 'distribute':
            const { amount } = broadcastData;
            notificationText = `${operator?.name} 出分到桌面 ${amount}`;
            break;

          case 'collect':
            const collectAmount = broadcastData.amount.actual;
            const nextRound = broadcastData.nextRoundTriggered;
            notificationText = `${operator?.name} 收分 ${collectAmount}${nextRound ? '，进入下一局' : ''}`;
            break;

          case 'tea_settings':
            const { settings } = broadcastData;
            notificationText = `${operator?.name} 修改了茶水设置：${settings.teaWaterRatio}%，上限${settings.limitText}`;
            break;

          case 'settlement':
            const { settlement } = broadcastData;
            notificationText = `${operator?.name} 生成了结算单，共${settlement.details.length}项，${settlement.totalLabel}：${settlement.totalAmount}`;
            break;

          case 'player_join':
            const { newPlayer } = broadcastData;
            notificationText = `${newPlayer.name} ${newPlayer.isReturning ? '重新' : ''}加入了房间`;
            break;

          case 'player_join_notification':
            const { newPlayer: joinedPlayer } = broadcastData.data || broadcastData;
            notificationText = `${joinedPlayer.name} 加入了房间`;
            break;

          case 'player_leave':
            const { playerLeft } = broadcastData;
            notificationText = `${playerLeft.name} 退出了房间`;
            break;

          default:
            notificationText = `${operator?.name} 进行了操作`;
        }

        // 显示通知（可以根据需要调整显示方式）
        console.log('房间通知:', notificationText);
        
        // 可以在这里添加UI通知，比如toast或者状态栏提示
        // showToast(notificationText, 'none');

      } catch (error) {
        console.error('显示广播通知失败:', error);
      }
    },

    /**
     * 处理玩家上线通知
     * @param {Object} data 上线数据
     */
    handlePlayerOnline(data) {
      try {
        const { userId, nickname, avatar, onlineCount } = data;
        
        console.log(`玩家上线: ${nickname}, 房间在线人数: ${onlineCount}`);
        
        // 更新在线人数显示
        this.updateOnlineCount(onlineCount);

        // 可以在这里添加上线提示
        showToast(`${nickname} 上线了`, 'none');

      } catch (error) {
        console.error('处理玩家上线通知失败:', error);
      }
    },

    /**
     * 处理玩家离线通知
     * @param {Object} data 离线数据
     */
    handlePlayerOffline(data) {
      try {
        const { userId, nickname, reason, onlineCount } = data;
        
        console.log(`玩家离线: ${nickname}, 原因: ${reason}, 房间在线人数: ${onlineCount}`);
        
        // 更新在线人数显示
        this.updateOnlineCount(onlineCount);

        // 可以在这里添加离线提示
        // showToast(`${nickname} 离线了`, 'none');

      } catch (error) {
        console.error('处理玩家离线通知失败:', error);
      }
    },

    /**
     * 处理新用户加入通知
     * @param {Object} data 加入通知数据
     */
    handlePlayerJoinNotification(data) {
      try {
        const { newPlayer, onlineCount, incremental } = data;
        
        console.log(`新用户加入: ${newPlayer.name}, 房间在线人数: ${onlineCount}`);
        
        // 处理增量更新
        if (incremental) {
          this.handleIncrementalUpdate(incremental, 'player_join_notification');
        }
        
        // 更新在线人数显示
        this.updateOnlineCount(onlineCount);

        // 显示加入提示
        showToast(`${newPlayer.name} 加入了房间`, 'none');

      } catch (error) {
        console.error('处理新用户加入通知失败:', error);
      }
    },

    /**
     * 处理欢迎消息
     * @param {Object} data 欢迎消息数据
     */
    handleWelcomeMessage(data) {
      try {
        const { message, systemMessage } = data;
        
        console.log('收到欢迎消息:', message);
        
        // 如果有系统消息，添加到消息列表
        if (systemMessage && this.messageData && Array.isArray(this.messageData)) {
          this.messageData.push(systemMessage);
          console.log('欢迎系统消息已添加到消息列表');
          
          // 滚动到底部显示新消息（如果是传统记分视图）
          if (this.isTraditionalView && this.scrollToBottom) {
            this.$nextTick(() => {
              this.scrollToBottom();
            });
          }
        }
        
        // 可以在这里显示欢迎提示
        // showToast(message, 'success');

      } catch (error) {
        console.error('处理欢迎消息失败:', error);
      }
    },

    /**
     * 更新房间在线人数
     * @param {number} count 在线人数
     */
    updateOnlineCount(count) {
      try {
        if (typeof count === 'number' && count >= 0) {
          this.roomOnlineCount = count;
          console.log('房间在线人数已更新:', count);
        }
      } catch (error) {
        console.error('更新在线人数失败:', error);
      }
    },

    /**
     * 切换在线状态显示
     * @param {boolean} show 是否显示
     */
    toggleOnlineStatus(show = null) {
      if (show === null) {
        this.showOnlineStatus = !this.showOnlineStatus;
      } else {
        this.showOnlineStatus = !!show;
      }
    },

    /**
     * 发送WebSocket消息
     * @param {string} action 操作类型
     * @param {Object} data 请求数据
     * @returns {Promise} 请求结果
     */
    sendWebSocketMessage(action, data) {
      return new Promise((resolve, reject) => {
        if (!this.isWebSocketEnabled) {
          reject(new Error('WebSocket未连接'));
          return;
        }

        // 生成去重键
        const deduplicationKey = `${action}_${JSON.stringify(data)}`;
        const now = Date.now();
        
        // 检查是否有重复请求（5秒内的相同请求视为重复）
        if (this.requestDeduplication.has(deduplicationKey)) {
          const existingRequest = this.requestDeduplication.get(deduplicationKey);
          if (now - existingRequest.timestamp < 5000) {
            console.warn(`检测到重复请求，忽略: ${action}`);
            reject(new Error('请求过于频繁，请稍后再试'));
            return;
          }
        }

        const requestId = this.generateRequestId();
        const message = {
          action,
          data: {
            token: this.wsConfig.token,
            roomId: this.wsConfig.roomId,
            ...data
          },
          requestId,
          timestamp: now
        };

        try {
          // 发送消息
          this.wsSocket.send({
            data: JSON.stringify(message)
          });

          // 存储待处理的请求
          this.pendingRequests.set(requestId, { resolve, reject, timestamp: now });
          
          // 记录去重信息
          this.requestDeduplication.set(deduplicationKey, { timestamp: now, requestId });

          // 设置超时处理
          setTimeout(() => {
            if (this.pendingRequests.has(requestId)) {
              this.pendingRequests.delete(requestId);
              this.requestDeduplication.delete(deduplicationKey);
              reject(new Error('请求超时'));
            }
          }, this.requestTimeout);

          console.log(`WebSocket消息已发送: ${action}, 请求ID: ${requestId}`);
        } catch (error) {
          console.error('发送WebSocket消息失败:', error);
          this.requestDeduplication.delete(deduplicationKey);
          reject(error);
        }
      });
    },

    /**
     * 断线重连
     */
    scheduleReconnect() {
      // 检查是否应该重连
      if (!this.shouldReconnectWebSocket()) {
        console.log('页面不可见或无房间信息，跳过WebSocket重连');
        return;
      }

      if (this.wsReconnectAttempts >= this.wsMaxReconnectAttempts) {
        console.log('WebSocket重连次数已达上限，停止重连');
        showToast('网络连接异常，已切换到普通模式', 'none');
        return;
      }

      const delay = Math.min(1000 * Math.pow(2, this.wsReconnectAttempts), 30000); // 指数退避，最大30秒
      this.wsReconnectAttempts++;

      console.log(`${delay}ms后尝试第${this.wsReconnectAttempts}次WebSocket重连`);
      
      this.wsReconnectTimer = setTimeout(() => {
        this.reconnectWebSocket();
      }, delay);
    },

    /**
     * 执行重连
     */
    async reconnectWebSocket() {
      if (this.wsConnected || this.wsConnecting) return;

      // 重连前再次检查条件，确保仍然满足重连要求
      if (!this.shouldReconnectWebSocket()) {
        console.log('重连执行时检查发现条件不满足，取消重连');
        return;
      }

      console.log(`正在进行第${this.wsReconnectAttempts}次WebSocket重连...`);
      await this.initWebSocket(this.wsConfig.roomId);
    },

    /**
     * 关闭WebSocket连接
     */
    closeWebSocket() {
      if (this.wsSocket) {
        this.wsSocket.close();
        this.wsSocket = null;
      }
      
      this.wsConnected = false;
      this.wsConnecting = false;
      
      // 清除重连定时器
      if (this.wsReconnectTimer) {
        clearTimeout(this.wsReconnectTimer);
        this.wsReconnectTimer = null;
      }
      
      // 清理待处理的请求
      this.clearPendingRequests('连接已关闭');
      
      // 重置重连计数
      this.wsReconnectAttempts = 0;
      
      // 清理WebSocket配置，防止意外重连
      if (this.wsConfig) {
        this.wsConfig.roomId = '';
        this.wsConfig.token = '';
      }
      
      console.log('WebSocket连接已关闭，配置已清理');
    },

    /**
     * 暂停WebSocket连接（页面隐藏时使用）
     */
    pauseWebSocket() {
      if (this.wsSocket) {
        console.log('暂停WebSocket连接');
        this.wsSocket.close();
        this.wsSocket = null;
      }
      
      this.wsConnected = false;
      this.wsConnecting = false;
      
      // 清除重连定时器，避免后台重连
      if (this.wsReconnectTimer) {
        clearTimeout(this.wsReconnectTimer);
        this.wsReconnectTimer = null;
      }
      
      // 清理待处理的请求
      this.clearPendingRequests('连接已暂停');
      
      console.log('WebSocket连接已暂停，配置保留用于恢复');
    },

    /**
     * 检查是否应该重连WebSocket
     * @returns {boolean} 是否应该重连
     */
    shouldReconnectWebSocket() {
      // 严格的重连条件检查：
      // 1. 必须有有效的WebSocket配置
      // 2. 必须有有效的房间信息
      // 3. 配置中的房间ID必须与当前房间ID匹配
      // 4. 房间数据必须加载完成且无错误
      // 5. uni对象必须存在（页面环境正常）
      
      const hasValidConfig = !!(this.wsConfig?.roomId && this.wsConfig?.token);
      const hasValidRoomInfo = !!(this.roomInfo?.id);
      const roomIdMatches = this.wsConfig?.roomId === this.roomInfo?.id;
      const roomDataReady = !this.isRoomDataLoading && !this.roomDataLoadError;
      const envReady = typeof uni !== 'undefined';
      
      const shouldReconnect = hasValidConfig && 
                             hasValidRoomInfo && 
                             roomIdMatches && 
                             roomDataReady && 
                             envReady;
      
      if (!shouldReconnect) {
        console.log('WebSocket重连条件检查失败:', {
          hasValidConfig,
          hasValidRoomInfo,
          roomIdMatches,
          roomDataReady,
          envReady,
          wsConfigRoomId: this.wsConfig?.roomId,
          roomInfoId: this.roomInfo?.id
        });
      }
      
      return shouldReconnect;
    },

    /**
     * 处理WebSocket错误
     * @param {Object} error 错误信息
     */
    handleWebSocketError(error) {
      console.error('WebSocket错误:', error);
      
      // 清理待处理的请求
      this.clearPendingRequests('连接错误');
      
      // 检查错误类型并处理
      this.handleConnectionException(error);
      
      // 不在用户界面显示技术错误，避免频繁打扰用户
      // 重连逻辑会自动处理网络问题
    },

    /**
     * 处理连接异常
     * @param {Object} error 错误信息
     */
    handleConnectionException(error) {
      try {
        // 记录异常信息
        const exceptionInfo = {
          timestamp: new Date(),
          error: error,
          roomId: this.wsConfig.roomId,
          reconnectAttempts: this.wsReconnectAttempts,
          connectionState: {
            connected: this.wsConnected,
            connecting: this.wsConnecting
          }
        };
        
        console.log('连接异常详情:', exceptionInfo);
        
        // 检查是否需要清理僵尸连接
        this.checkAndCleanupZombieConnection();
        
        // 如果重连次数过多，提示用户
        if (this.wsReconnectAttempts >= this.wsMaxReconnectAttempts) {
          this.handleMaxReconnectReached();
        }
        
      } catch (handlerError) {
        console.error('处理连接异常失败:', handlerError);
      }
    },

    /**
     * 检查并清理僵尸连接
     */
    checkAndCleanupZombieConnection() {
      try {
        // 如果连接状态异常，强制清理
        if (this.wsSocket && (!this.wsConnected && !this.wsConnecting)) {
          console.log('检测到僵尸连接，正在清理...');
          
          try {
            this.wsSocket.close();
          } catch (closeError) {
            console.warn('关闭僵尸连接失败:', closeError);
          }
          
          this.wsSocket = null;
          this.wsConnected = false;
          this.wsConnecting = false;
          
          console.log('僵尸连接清理完成');
        }
      } catch (error) {
        console.error('清理僵尸连接失败:', error);
      }
    },

    /**
     * 处理达到最大重连次数
     */
    handleMaxReconnectReached() {
      try {
        console.warn('WebSocket重连次数已达上限，停止重连');
        
        // 清理重连定时器
        if (this.wsReconnectTimer) {
          clearTimeout(this.wsReconnectTimer);
          this.wsReconnectTimer = null;
        }
        
        // 重置连接状态
        this.wsConnected = false;
        this.wsConnecting = false;
        this.wsSocket = null;
        
        // 提示用户网络异常
        showToast('网络连接异常，已切换到普通模式', 'none');
        
        // 可以在这里添加重试按钮或其他用户交互
        this.showConnectionRetryOption();
        
      } catch (error) {
        console.error('处理最大重连次数失败:', error);
      }
    },

    /**
     * 显示连接重试选项
     */
    showConnectionRetryOption() {
      try {
        // 这里可以显示一个重试按钮或者其他UI提示
        // 暂时只记录日志，具体UI实现可以根据需要添加
        console.log('可以显示重试连接的UI选项');
        
        // 示例：可以设置一个标志位，让UI显示重试按钮
        // this.showRetryButton = true;
        
      } catch (error) {
        console.error('显示连接重试选项失败:', error);
      }
    },

    /**
     * 手动重试连接
     */
    async manualRetryConnection() {
      try {
        console.log('用户手动重试WebSocket连接');
        
        // 手动重连前检查基本条件
        if (!this.roomInfo?.id || !this.wsConfig?.roomId) {
          console.log('手动重连检查失败，房间信息或配置不完整');
          showToast('房间信息异常，无法重连', 'none');
          return;
        }
        
        // 重置重连计数
        this.wsReconnectAttempts = 0;
        
        // 清理现有连接
        this.closeWebSocket();
        
        // 等待一段时间后重新连接
        setTimeout(() => {
          if (this.shouldReconnectWebSocket()) {
            this.initWebSocket(this.roomInfo.id);
          } else {
            console.log('手动重连延时检查失败，取消重连');
          }
        }, 1000);
        
      } catch (error) {
        console.error('手动重试连接失败:', error);
        showToast('重试连接失败，请稍后再试', 'none');
      }
    },

    /**
     * 检查连接健康状态
     */
    checkConnectionHealth() {
      try {
        if (!this.wsConnected || !this.wsSocket) {
          return false;
        }
        
        // 检查是否有长时间未响应的请求
        const now = Date.now();
        let hasStaleRequests = false;
        
        for (const [requestId, request] of this.pendingRequests) {
          if (now - request.timestamp > this.requestTimeout * 2) {
            console.warn(`检测到超时请求: ${requestId}`);
            hasStaleRequests = true;
          }
        }
        
        if (hasStaleRequests) {
          console.warn('连接可能存在问题，清理超时请求');
          this.clearPendingRequests('连接健康检查');
        }
        
        return !hasStaleRequests;
        
      } catch (error) {
        console.error('检查连接健康状态失败:', error);
        return false;
      }
    },

    /**
     * 清理待处理的请求
     * @param {string} reason 清理原因
     */
    clearPendingRequests(reason) {
      const requestCount = this.pendingRequests.size;
      for (const [requestId, request] of this.pendingRequests) {
        request.reject(new Error(reason));
      }
      this.pendingRequests.clear();
      this.requestDeduplication.clear(); // 同时清理去重记录
      console.log(`已清理${requestCount}个待处理的WebSocket请求: ${reason}`);
    },

    /**
     * 生成请求ID
     * @returns {string} UUID格式的请求ID
     */
    generateRequestId() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0;
        const v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },

    // ==================== 业务WebSocket调用方法 ====================

    /**
     * WebSocket单人给分
     * @param {Object} player 目标玩家
     * @param {number} score 分数
     * @returns {Promise} 操作结果
     */
    async wsScoreSinglePlayer(player, score) {
      console.log(player, score, "player, score");
      try {
        const result = await this.sendWebSocketMessage('single_score', {
          player: { id: player.id, name: player.name, avatar_fileId: player.avatarFileId || "" },
          score: score
        });
        console.log('WebSocket单人给分成功:', result);
        return { success: true, data: result };
      } catch (error) {
        console.error('WebSocket单人给分失败:', error);
        return { success: false, message: error.message };
      }
    },

    /**
     * WebSocket多人给分
     * @param {Array} entries 给分条目 [{ player, score }]
     * @returns {Promise} 操作结果
     */
    async wsScoreMultiplePlayers(entries) {
      try {
        const result = await this.sendWebSocketMessage('multiple_score', {
          entries: entries.map(entry => ({
            player: { id: entry.player.id, name: entry.player.name, avatar_fileId: entry.player.avatarFileId || "" },
            score: entry.score
          }))
        });
        console.log('WebSocket多人给分成功:', result);
        return { success: true, data: result };
      } catch (error) {
        console.error('WebSocket多人给分失败:', error);
        return { success: false, message: error.message };
      }
    },

    /**
     * WebSocket出分到桌面
     * @param {number} amount 出分金额
     * @returns {Promise} 操作结果
     */
    async wsDistributeScore(amount) {
      try {
        const result = await this.sendWebSocketMessage('distribute', {
          amount: amount
        });
        console.log('WebSocket出分成功:', result);
        return { success: true, data: result };
      } catch (error) {
        console.error('WebSocket出分失败:', error);
        return { success: false, message: error.message };
      }
    },

    /**
     * WebSocket收分
     * @param {number} amount 收分金额
     * @returns {Promise} 操作结果
     */
    async wsCollectScore(amount) {
      try {
        const result = await this.sendWebSocketMessage('collect', {
          amount: amount
        });
        console.log('WebSocket收分成功:', result);
        return { success: true, data: result };
      } catch (error) {
        console.error('WebSocket收分失败:', error);
        return { success: false, message: error.message };
      }
    },

    /**
     * WebSocket保存茶水设置
     * @param {Object} teaSettings 茶水设置
     * @returns {Promise} 操作结果
     */
    async wsSaveTeaSettings(teaSettings) {
      try {
        const result = await this.sendWebSocketMessage('tea_settings', {
          teaSettings: teaSettings
        });
        console.log('WebSocket茶水设置成功:', result);
        return { success: true, data: result };
      } catch (error) {
        console.error('WebSocket茶水设置失败:', error);
        return { success: false, message: error.message };
      }
    },

    /**
     * WebSocket茶水记分
     * @param {Object} teaScoreData 茶水记分数据
     * @returns {Promise} 操作结果
     */
    async wsTeaScore(teaScoreData) {
      try {
        const result = await this.sendWebSocketMessage('tea_score', {
          amount: teaScoreData.amount
        });
        console.log('WebSocket茶水记分成功:', result);
        return { success: true, data: result };
      } catch (error) {
        console.error('WebSocket茶水记分失败:', error);
        return { success: false, message: error.message };
      }
    },

    /**
     * WebSocket玩家加入房间
     * @param {string} joinCode 房间邀请码
     * @returns {Promise} 操作结果
     */
    async wsJoinRoom(joinCode) {
      try {
        const result = await this.sendWebSocketMessage('player_join', {
          joinCode: joinCode
        });
        console.log('WebSocket玩家加入成功:', result);
        return { success: true, data: result };
      } catch (error) {
        console.error('WebSocket玩家加入失败:', error);
        return { success: false, message: error.message };
      }
    },

    /**
     * WebSocket生成结算单
     * @param {Object} settlementData 结算单数据 { details, totalLabel, totalAmount, scoreAdjustments, newTeaBalance }
     * @returns {Promise} 操作结果
     */
    async wsGenerateSettlement(settlementData) {
      try {
        const result = await this.sendWebSocketMessage('settlement', {
          settlementData: settlementData
        });
        console.log('WebSocket生成结算单成功:', result);
        return { success: true, data: result };
      } catch (error) {
        console.error('WebSocket生成结算单失败:', error);
        return { success: false, message: error.message };
      }
    },

    /**
     * WebSocket退出房间
     * @returns {Promise} 操作结果
     */
    async wsLeaveRoom() {
      try {
        const result = await this.sendWebSocketMessage('player_leave', {});
        console.log('WebSocket退出房间成功:', result);
        return { success: true, data: result };
      } catch (error) {
        console.error('WebSocket退出房间失败:', error);
        return { success: false, message: error.message };
      }
    }
  },

  // ==================== 生命周期管理 ====================
  
  beforeDestroy() {
    // 清理WebSocket连接
    this.closeWebSocket();
  }
}; 