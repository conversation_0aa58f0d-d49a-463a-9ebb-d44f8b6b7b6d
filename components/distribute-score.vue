<template>
  <div class="distribute-modal" v-if="visible">
    <div class="distribute-modal-content">
      <div class="modal-header">
        <div class="modal-title">出分</div>
        <div class="modal-close" @click="closeModal">
          <span class="iconfont close-icon">&#xe874;</span>
        </div>
      </div>

      <div class="distribute-form">
        <div class="form-item">
          <input
            class="distribute-input"
            type="number"
            v-model="distributeAmount"
            placeholder="请输入整数分数"
            min="1"
            step="1"
            pattern="[0-9]*"
            @input="handleDistributeInput"
            cursor-spacing="140"
          />
        </div>
        
        <button class="confirm-distribute-button" @click="confirmDistribute">
          确定出分
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "DistributeScore",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    activePlayers: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      distributeAmount: null,
    };
  },
  methods: {
    closeModal() {
      this.distributeAmount = null;
      this.$emit("close");
    },
    confirmDistribute() {
      // 检查房间玩家数量，只有一个玩家时不能出分
      if (this.activePlayers && this.activePlayers.length <= 1) {
        uni.showToast({
          title: "房间只有一个玩家，无法出分",
          icon: "none",
        });
        return;
      }

      // 处理出分到桌面的逻辑
      if (!this.distributeAmount) {
        uni.showToast({
          title: "请输入出分数额",
          icon: "none",
        });
        return;
      }

      const amount = Number(this.distributeAmount);

      // 验证输入是否为正整数
      if (isNaN(amount) || amount <= 0 || !Number.isInteger(amount)) {
        uni.showToast({
          title: "请输入有效的正整数",
          icon: "none",
        });
        return;
      }

      // 验证分数不能超过99999
      if (amount > 99999) {
        uni.showToast({
          title: "分数不能超过99999",
          icon: "none",
        });
        return;
      }

      // 发送确认事件给父组件
      this.$emit("confirm", amount);

      // 清空输入并关闭弹窗
      this.distributeAmount = null;
    },
    handleDistributeInput(event) {
      // 处理输入框的输入事件，确保只输入数字字符
      const inputValue = event.target.value;

      if (inputValue) {
        // 移除非数字字符
        const cleanValue = inputValue.replace(/[^0-9]/g, "");
        this.distributeAmount = cleanValue;
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

/* 出分到桌面弹窗样式 */
.distribute-modal {
  @include modal-overlay;
  background-color: rgba(0, 0, 0, 0.6);
}

.distribute-modal-content {
  @include modal-content;
  background-color: $card-background;
}

.modal-header {
  @include modal-header;
  background-color: $card-background;
  border-bottom: 1px solid $border-color;
  padding: $spacing-md $spacing-lg;

  .modal-title {
    font-size: $font-size-lg;
    color: $text-primary;
    font-weight: $font-weight-semibold;
  }
}

.modal-close {
  @include button-text;
  width: 32px;
  height: 32px;
  padding: 0;
  color: $text-muted;
  border-radius: $border-radius-round;
  
  &:hover {
    background-color: $background-color;
    color: $text-secondary;
  }

  &:active {
    background-color: $border-light;
  }

  .close-icon {
    font-size: 18px;
  }
}

.distribute-form {
  width: 100%;
  padding: $spacing-xl;
  display: flex;
  flex-direction: column;
  gap: $spacing-lg;
}

.form-item {
  display: flex;
  justify-content: center;
  align-items: center;
}

.distribute-input {
  @include input-base;
  width: 100%;
  max-width: 300px;
  height: 50px;
  font-size: $font-size-lg;
  text-align: center;
  font-weight: $font-weight-medium;
}

.confirm-distribute-button {
  @include button-primary;
  width: 100%;
  height: 44px;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  border-radius: $border-radius-base;

  &:active {
    background-color: $primary-dark;
  }
}
</style> 