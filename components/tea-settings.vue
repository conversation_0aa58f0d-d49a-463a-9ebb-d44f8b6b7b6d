<template>
  <div class="tea-settings-modal" v-if="visible">
    <div class="tea-settings-content">
      <div class="modal-header">
        <div class="modal-title">自动茶水设置</div>
        <div class="modal-close" @click="handleClose">
          <span class="iconfont close-icon">&#xe874;</span>
        </div>
      </div>

      <div class="tea-settings-list">
        <div class="tea-settings-item">
          <div class="setting-label">
            <span>茶水金额上限</span>
            <span class="help-icon" @click="showLimitHelp">?</span>
          </div>
          <div class="input-container">
            <input
              type="number"
              v-model="limitAmount"
              class="limit-input"
              placeholder="不限制"
            />
          </div>
        </div>

        <div class="tea-settings-item">
          <div class="setting-label">
            <span>每笔抽取比例</span>
            <span class="help-icon" @click="showRatioHelp">?</span>
          </div>
          <div class="ratio-input-container">
            <input
              type="number"
              v-model="ratio"
              class="ratio-input"
              min="0"
              max="10"
              @blur="validateRatio"
            />
            <span class="ratio-unit">%</span>
          </div>
        </div>
      </div>

      <button class="save-button" @click="handleSave">保存设置</button>
    </div>
  </div>
</template>

<script>
export default {
  name: "TeaSettings",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    teaWaterLimitAmount: {
      type: [Number, null],
      default: null,
    },
    teaWaterRatio: {
      type: Number,
      default: 0,
    },
  },
  data() {
    return {
      limitAmount: null,
      ratio: 0,
    };
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        // 当弹窗显示时，初始化数据
        this.limitAmount = this.teaWaterLimitAmount;
        this.ratio = this.teaWaterRatio;
      }
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleSave() {
      // 验证茶水比例
      this.validateRatio();

      // 准备上限显示文本
      const limitText = this.limitAmount
        ? this.limitAmount + "分"
        : "不限制";

      // 发送保存事件
      this.$emit("save", {
        limitAmount: this.limitAmount,
        ratio: this.ratio,
        limitText: limitText,
      });
    },
    validateRatio() {
      // 验证比例是否超过上限
      if (this.ratio > 10) {
        this.ratio = 10;
      }
    },
    showLimitHelp() {
      // 显示茶水上限的帮助信息
      uni.showModal({
        title: "茶水金额上限",
        content: "设置茶水抽取的最大金额，留空表示不限制。",
        showCancel: false,
        confirmText: "知道了",
      });
    },
    showRatioHelp() {
      // 显示抽取比例的帮助信息
      uni.showModal({
        title: "每笔抽取比例",
        content: "设置每笔交易中抽取的茶水比例，范围0-10%。",
        showCancel: false,
        confirmText: "知道了",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

/* 茶水设置弹窗样式 */
.tea-settings-modal {
  @include modal-overlay;
  background-color: rgba(0, 0, 0, 0.6);
  align-items: flex-start;
  padding-top: 15vh; /* 距离顶部15%的位置，避免键盘遮挡 */

  /* 移动端优化 */
  @media (max-height: 600px) {
    padding-top: 10vh; /* 小屏幕设备使用更小的顶部距离 */
  }
}

.tea-settings-content {
  @include modal-content;
  width: 80%;
  max-width: 360px;
  background-color: $card-background;
  border-radius: $border-radius-lg;
  max-height: none;
  border: 1px solid $border-light;
  box-shadow: $box-shadow-lg;
  padding: 0 0 $spacing-lg 0;
}

.modal-header {
  @include modal-header;
  background-color: $card-background;
  border-bottom: 1px solid $border-color;
  border-radius: $border-radius-lg $border-radius-lg 0 0;
  padding: $spacing-md $spacing-lg;

  .modal-title {
    font-size: $font-size-lg;
    color: $text-primary;
    font-weight: $font-weight-semibold;
  }
}

.modal-close {
  @include button-text;
  width: 32px;
  height: 32px;
  padding: 0;
  color: $text-muted;
  border-radius: $border-radius-round;
  
  &:hover {
    background-color: $background-color;
    color: $text-secondary;
  }

  &:active {
    background-color: $border-light;
  }

  .close-icon {
    font-size: 18px;
  }
}

.tea-settings-list {
  width: 100%;
  padding: $spacing-md 0;
  min-height: 160px;
  max-height: 300px;
  overflow-y: auto;
}

.tea-settings-item {
  @include list-item;
  justify-content: space-between;
  padding: $spacing-md $spacing-lg;
  margin-bottom: $spacing-sm;
}

.setting-label {
  display: flex;
  align-items: center;
  font-size: $font-size-base;
  color: $text-primary;
  font-weight: $font-weight-medium;
}

.help-icon {
  @include icon-container(20px, $background-color);
  margin-left: $spacing-sm;
  font-size: $font-size-xs;
  color: $text-secondary;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: $border-color;
    color: $text-primary;
  }
}

.input-container {
  display: flex;
  align-items: center;
}

.limit-input {
  @include input-base;
  width: 100px;
  height: 36px;
  font-size: $font-size-base;
  text-align: center;
  padding-left: $spacing-base;
}

.ratio-input-container {
  display: flex;
  align-items: center;
  gap: $spacing-xs;
}

.ratio-input {
  @include input-base;
  width: 60px;
  height: 36px;
  font-size: $font-size-base;
  text-align: center;
  padding-left: $spacing-base;
}

.ratio-unit {
  font-size: $font-size-base;
  color: $text-secondary;
  font-weight: $font-weight-medium;
}

.save-button {
  @include button-primary;
  width: 90%;
  height: 44px;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  border-radius: $border-radius-xl;
  margin: $spacing-md auto 0;
  display: block;
  line-height: 28px;
  &:active {
    background-color: $primary-dark;
  }
}
</style> 