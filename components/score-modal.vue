<template>
  <base-modal
    :visible="visible"
    :title="modalType !== 'tea' ? defaultTitle : ''"
    @close="handleClose"
  >
    <!-- 茶水记分时的自定义标题 -->
    <template #header v-if="modalType === 'tea'">
      <div class="modal-header">
        <div class="tea-modal-title">
          <span class="iconfont settings-icon" @click="handleSettingsClick">&#xe8b2;</span>
          <span class="title-text">给茶水记分</span>
        </div>
        <div class="modal-close" @click="handleClose">
          <span class="iconfont close-icon">&#xe874;</span>
        </div>
      </div>
    </template>
    <!-- 主体内容 -->
    <div class="players-list-container">
      <scroll-view
        class="players-scroll-view"
        scroll-y="true"
        :show-scrollbar="false"
      >
        <div
          class="player-score-item"
          v-for="player in displayPlayers"
          :key="player.id"
        >
          <div class="player-info-left">
            <div class="player-avatar">
              <avatar-display
                v-if="player.id !== 'tea_water'"
                :avatarFileId="player.avatarFileId"
                size="50px"
              />
              <div
                v-else
                class="avatar function-avatar tea-avatar"
              >
                <span class="iconfont function-icon">&#xe878;</span>
              </div>
            </div>
            <div class="player-name">{{ player.name }}</div>
          </div>
          <div class="player-input-right">
            <input
              class="amount-input"
              type="digit"
              maxlength="6"
              v-model="player.inputAmount"
              :placeholder="modalType === 'tea' ? '填写茶水金额' : '填写支出金额'"
              confirm-type="done"
              cursor-spacing="140"
            />
          </div>
        </div>
      </scroll-view>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <button class="confirm-score-button" @click="handleConfirm">
        确定计分
      </button>
    </template>
  </base-modal>
</template>

<script>
import BaseModal from "./base-modal.vue";
import AvatarDisplay from "@/components/avatar-display.vue";

export default {
  name: "ScoreModal",
  components: {
    BaseModal,
    AvatarDisplay,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    modalType: {
      type: String,
      default: "single", // 'single' 或 'multiple'
    },
    selectedPlayer: {
      type: Object,
      default: null,
    },
    players: {
      type: Array,
      default: () => [],
    },
    currentUserId: {
      type: String,
      default: "",
    },
  },
  computed: {
    // 默认标题
    defaultTitle() {
      if (this.modalType === 'tea') {
        return '给茶水记分';
      } else if (this.modalType === 'single') {
        return `给 ${this.selectedPlayer?.name} 计分`;
      } else {
        return '记录一笔支出';
      }
    },

    displayPlayers() {
      if (this.modalType === "single" && this.selectedPlayer) {
        // 单个玩家计分，只显示选中的玩家
        return [
          {
            ...this.selectedPlayer,
            inputAmount: "",
          },
        ];
      } else if (this.modalType === "multiple") {
        // 多个玩家计分，过滤掉当前用户
        return this.players
          .filter((player) => player.id !== this.currentUserId)
          .map((player) => ({
            ...player,
            inputAmount: "",
          }));
      } else if (this.modalType === "tea") {
        // 茶水记分，显示一个虚拟的茶水"玩家"
        return [
          {
            id: "tea_water",
            name: "茶水",
            avatarFileId: null,
            inputAmount: "",
          },
        ];
      }
      return [];
    },
  },
  methods: {
    handleClose() {
      this.$emit("close");
    },
    handleSettingsClick() {
      this.$emit("settings");
      this.handleClose();
    },
    handleConfirm() {
      if (this.modalType === "single") {
        // 单个玩家计分
        const player = this.displayPlayers[0];
        if (!player.inputAmount || isNaN(Number(player.inputAmount))) {
          uni.showToast({
            title: "请输入有效的分数",
            icon: "none",
          });
          return;
        }

        const score = Number(player.inputAmount);

        // 传递计分数据
        this.$emit("confirm", {
          type: "single",
          player: player,
          score: score,
        });
      } else if (this.modalType === "tea") {
        // 茶水记分
        const teaData = this.displayPlayers[0];
        if (!teaData.inputAmount || isNaN(Number(teaData.inputAmount))) {
          uni.showToast({
            title: "请输入有效的分数",
            icon: "none",
          });
          return;
        }

        const score = Number(teaData.inputAmount);

        // 传递茶水记分数据
        this.$emit("confirm", {
          type: "tea",
          score: score,
        });
      } else if (this.modalType === "multiple") {
        // 多个玩家计分
        const validEntries = this.displayPlayers.filter(
          (player) =>
            player.inputAmount &&
            !isNaN(Number(player.inputAmount)) &&
            Number(player.inputAmount) > 0
        );

        if (validEntries.length === 0) {
          uni.showToast({
            title: "请至少为一个玩家输入有效的分数",
            icon: "none",
          });
          return;
        }

        // 传递计分数据
        this.$emit("confirm", {
          type: "multiple",
          entries: validEntries.map((player) => ({
            player: player,
            score: Number(player.inputAmount),
          })),
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
@import "@/styles/common.scss";

/* 计分内容样式 */
.players-list-container {
  padding: 0 $spacing-lg;
  max-height: calc(70vh - 140px);
  overflow-y: auto;
}

.players-scroll-view {
  flex: 1;
  height: 100%;
  width: 100%;
  padding: $spacing-md;
}

.player-score-item {
  @include list-item;
  justify-content: space-between;
  padding: $spacing-md 0;
}

.player-info-left {
  display: flex;
  align-items: center;
  gap: $spacing-md;
}

.player-avatar {
  @include avatar(40px);
}

/* 茶水记分弹窗标题样式 */
.tea-modal-title {
  display: flex;
  align-items: center;
  gap: $spacing-sm;

  .settings-icon {
    font-size: 18px;
    color: $primary-color;
    cursor: pointer;
    padding: $spacing-xs;
    border-radius: $border-radius-round;
    transition: all 0.2s ease;

    &:hover {
      background-color: rgba($primary-color, 0.1);
    }

    &:active {
      transform: scale(0.95);
      background-color: rgba($primary-color, 0.2);
    }
  }

  .title-text {
    font-size: $font-size-lg;
    font-weight: $font-weight-semibold;
    color: $text-primary;
  }
}

/* 使用与页面一致的茶水头像样式 */
.function-avatar {
  @include icon-container(50px, $primary-light);
  border: 1px solid $border-color;

  &:hover {
    border-color: $primary-color;
  }

  &.tea-avatar {
    background: linear-gradient(135deg, #fef3c7 0%, #fbbf24 100%);
    border-color: #f59e0b;

    &:hover {
      border-color: #d97706;
    }

    .function-icon {
      color: #92400e;
    }
  }
}

.player-name {
  font-size: $font-size-base;
  color: $text-primary;
  font-weight: $font-weight-medium;
}

.player-input-right {
  flex-shrink: 0;
  min-width: 120px;
}

.amount-input {
  @include input-base;
  text-align: right;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  padding-left: $spacing-base;
  border-radius: $border-radius-base;
  padding-right: $spacing-base;
  width: 100%;
  height: 50px;
  transition: all 0.2s ease;

  &:focus {
    border-color: $primary-color;
    box-shadow: 0 0 0 2px rgba(7, 193, 96, 0.1);
  }
}

.confirm-score-button {
  @include button-primary;
  width: 100%;
  height: 44px;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  border-radius: $border-radius-base;

  &:active {
    background-color: $primary-dark;
  }
}
</style>
